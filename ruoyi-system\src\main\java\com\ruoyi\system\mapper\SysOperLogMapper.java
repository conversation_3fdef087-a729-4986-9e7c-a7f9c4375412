package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.domain.SysOperLog;

/**
 * 操作日志 数据层
 * 
 * <AUTHOR>
 */
public interface SysOperLogMapper
{
    /**
     * 新增操作日志
     * 
     * @param operLog 操作日志对象
     */
    public void insertOperlog(SysOperLog operLog);

    /**
     * 查询系统操作日志集合
     * 
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    public List<SysOperLog> selectOperLogList(SysOperLog operLog);

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public int deleteOperLogByIds(Long[] operIds);

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLog selectOperLogById(Long operId);

    /**
     * 清空操作日志
     */
    public void cleanOperLog();

    /**
     * 获取成功操作的统计信息
     */
    public List<Map<String, Object>> getSuccessOperationStats(SysOperLog operLog);

    /**
     * 获取失败操作的统计信息
     */
    public List<Map<String, Object>> getFailureOperationStats(SysOperLog operLog);

    /**
     * 获取按状态分类的操作统计信息
     */
    public List<Map<String, Object>> getStatusStats(SysOperLog operLog);

    /** 
     * 获取按模块和操作类型分类的操作统计信息
     */
    public List<Map<String, Object>> getModuleOperationStats(SysOperLog operLog);
}
