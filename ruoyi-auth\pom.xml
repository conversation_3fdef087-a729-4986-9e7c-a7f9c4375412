<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi.geekxd</groupId>
        <version>3.9.0-G</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-auth</artifactId>

    <properties>
        <justauth.version>1.16.7</justauth.version>
        <alipay.version>3.7.4.ALL</alipay.version>
        <dysmsapi.version>3.1.1</dysmsapi.version>
        <mail.version>2.0.1</mail.version>
    </properties>

    <description>
        第三方认证模块
    </description>
    <dependencyManagement>
        <dependencies>
            <!-- 第三方认证通用工具-->
            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-auth-common</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.mail</groupId>
                <artifactId>jakarta.mail-api</artifactId>
                <version>${mail.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>jakarta.mail</artifactId>
                <version>${mail.version}</version>
            </dependency>

            <!-- justauth第三方认证框架 -->
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${justauth.version}</version>
            </dependency>

            <!-- 支付宝开发者sdk -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 阿里云短信认证 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${dysmsapi.version}</version>
            </dependency>

            <!-- justauth通用认证 -->
            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-oauth-justauth</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 微信小程序和公众号认证 -->
            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-oauth-wx</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 手机号认证 -->
            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-tfa-phone</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 邮箱认证 -->
            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-tfa-email</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 第三方登录启动器 -->
            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-auth-starter</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <modules>
        <module>ruoyi-auth-common</module>
        <module>ruoyi-oauth-justauth</module>
        <module>ruoyi-oauth-wx</module>
        <module>ruoyi-tfa-phone</module>
        <module>ruoyi-tfa-email</module>
        <module>ruoyi-auth-starter</module>
    </modules>
    <packaging>pom</packaging>
</project>