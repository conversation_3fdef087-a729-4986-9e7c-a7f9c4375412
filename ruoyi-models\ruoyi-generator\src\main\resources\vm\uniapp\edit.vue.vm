<template>
  <view>
        <uni-forms ref="form" :model="form" :rules="rules" label-width="80px">
#foreach($column in $columns)
#set($field=$column.javaField)
#if($column.insert && !$column.pk)
#if(($column.usableColumn) || (!$column.superColumn))
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#set($dictType=$column.dictType)
#if($column.htmlType == "input")
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-easyinput v-model="form.${field}" placeholder="请输入${comment}" />
        </uni-forms-item>
#elseif($column.htmlType == "imageUpload")
        <uni-forms-item label="${comment}" prop="${field}">
          <u-upload
            :fileList="form.${field}"
            @afterRead="afterRead"
            @delete="deletePic"
            name="3"
            multiple
            :maxCount="10"
            :previewFullImage="true"
          ></u-upload>
        </uni-forms-item>
#elseif($column.htmlType == "fileUpload")
        <uni-forms-item label="${comment}" prop="${field}">
          <!-- <file-upload v-model="form.${field}"/> -->
        </uni-forms-item>
#elseif($column.htmlType == "editor")
        <uni-forms-item label="${comment}">
          <!-- <editor v-model="form.${field}" :min-height="192"/> -->
        </uni-forms-item>
#elseif($column.htmlType == "select" && "" != $dictType)
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-data-select
            v-for="dict in ${field}Options"
            :key="dict.dictValue"
            :text="dict.dictLabel"
            :value="dict.dictValue"
          />
        </uni-forms-item>
#elseif($column.htmlType == "select" && $dictType)
        <uni-forms-item label="${comment}" prop="${field}">
            <uni-data-select label="请选择字典生成" value="" />
        </uni-forms-item>
#elseif($column.htmlType == "checkbox" && "" != $dictType)
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-data-checkbox v-model="form.${field}" multiple :localdata="${field}Options"></uni-data-checkbox>
        </uni-forms-item>
#elseif($column.htmlType == "checkbox" && $dictType)
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-data-checkbox v-model="form.${field}" multiple :localdata="[{text:'请选择字典生成'}]"></uni-data-checkbox>
        </uni-forms-item>
#elseif($column.htmlType == "radio" && "" != $dictType)
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-data-checkbox v-model="form.${field}" :localdata="${field}Options" />
        </uni-forms-item>
#elseif($column.htmlType == "radio" && $dictType)
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-data-checkbox v-model="form.${field}" :localdata="[{text:'请选择字典生成'}]"></uni-data-checkbox>
        </uni-forms-item>
#elseif($column.htmlType == "time")
        <uni-forms-item label="${comment}" prop="${field}">
          <picker mode="time" :value="form.${field}" start="00:00:00" end="23:59:59">
						  <view>{{form.${field}}}</view>
					</picker>
        </uni-forms-item>
#elseif($column.htmlType == "date")
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-datetime-picker type="date" v-model="form.${field}"/>
        </uni-forms-item>
#elseif($column.htmlType == "datetime")
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-datetime-picker type="datetime" v-model="form.${field}"/>
        </uni-forms-item>
#elseif($column.htmlType == "textarea")
        <uni-forms-item label="${comment}" prop="${field}">
          <uni-easyinput v-model="form.${field}" type="textarea" placeholder="请输入内容" />
        </uni-forms-item>
#end
#end
#end
#end
      </uni-forms>
  </view>
</template>

<script setup>
import { get${BusinessName}, del${BusinessName}, add${BusinessName}, update${BusinessName} } from "@/api/${moduleName}/${businessName}";
import {onLoad,onShow} from "@dcloudio/uni-app";
import { ref } from "vue";
#if(${dicts} != '')
import DictTag from "@/components/dict-tag/dict-tag; 
import { getDicts } from "@/api/system/dict/data";
#end

#foreach($column in $columns)    
#set($javaField=$column.javaField)
#if("" != $column.dictType)
const ${javaField}Options = ref([])
#end
#end
// 表单参数
const ${businessName} = ref({
#foreach ($column in $columns)
#if($column.htmlType == "checkbox")
        $column.javaField: []#if($foreach.count != $columns.size()),#end
#else
        $column.javaField: null#if($foreach.count != $columns.size()),#end
#end
#end
      })
onShow(params=>{
  if(params.${pkColumn.javaField}){
      get${BusinessName}(params.${pkColumn.javaField}).then(res=>{
        ${businessName}.value = res.data
      })
    }
})
onLoad(()=> {
#foreach($column in $columns)   
#set($javaField=$column.javaField) 
#if("" != $column.dictType)  
  getDicts(${column.dictType}).then(response => {
    ${javaField}Options.value = []
    for(let opt in response.data){
      ${javaField}Options.value.push({text: opt.dictLabel,value: opt.dictValue})
    }
  });
#end
#end  
})

/** 删除按钮操作 */
function handleDelete(${pkColumn.javaField}) {
  del${BusinessName}(${pkColumn.javaField}).then(()=>{
    navigateTo("/pages/${moduleName}/${businessName}/list")
  })
}
/** ${subTable.functionName}添加按钮操作 */
function handleAdd${BusinessName}() {
  add${BusinessName}(${businessName}.value).then(()=>{
    navigateTo("/pages/${moduleName}/${businessName}/list")
  })
}
function navigateTo(url){
    tab.navigateTo(url)
}
</script>
