<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi.geekxd</groupId>
        <version>3.9.0-G</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-file</artifactId>

    <properties>
        <aliyunoss.version>3.18.1</aliyunoss.version>
        <minio.version>8.2.1</minio.version>
    </properties>

    <description>
        文件模块
    </description>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-file-common</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- oss-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyunoss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-file-oss-alibaba</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ruoyi.geekxd</groupId>
                <artifactId>ruoyi-file-minio</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <modules>
        <module>ruoyi-file-common</module>
        <module>ruoyi-file-minio</module>
        <module>ruoyi-file-oss-alibaba</module>
        <module>ruoyi-file-starter</module>
    </modules>
    <packaging>pom</packaging>
</project>