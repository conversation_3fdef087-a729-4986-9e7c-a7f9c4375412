<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.flowable.mapper.FlowDeployMapper">
    

    <select id="selectDeployList" resultType="com.ruoyi.flowable.domain.dto.FlowProcDefDto">

        SELECT
            rp.id_ as id,
            rp.deployment_id_ as deploymentId,
            rd.name_ as name,
            rd.category_ as category,
            rp.key_ as flowKey,
            rp.version_ as version,
            rp.suspension_state_ as suspensionState,
            rd.deploy_time_  as deploymentTime
        FROM
            act_re_procdef rp
                LEFT JOIN act_re_deployment rd ON rp.deployment_id_ = rd.id_
        <where>
            <if test="name != null and name != ''">
               and rd.name_ like concat('%', #{name}, '%')
            </if>
        </where>
        order by rd.deploy_time_ desc
    </select>


</mapper>