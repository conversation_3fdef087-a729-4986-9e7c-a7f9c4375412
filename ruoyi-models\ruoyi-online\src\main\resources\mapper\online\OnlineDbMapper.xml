<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.online.mapper.OnlineDbMapper">
    <select id="selectDbTableList" resultType="java.util.Map">
        select * from information_schema.tables
		where table_schema = (select database())
	</select>
   
    <select id="selectDbColumnsListByTableName" parameterType="String" resultType="java.util.Map">
        select * from information_schema.columns 
        where  table_schema = (select database())
        and table_name = #{tableName};
	</select>
</mapper>