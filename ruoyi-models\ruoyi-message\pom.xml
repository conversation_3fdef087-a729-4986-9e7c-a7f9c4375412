<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi-models</artifactId>
        <groupId>com.ruoyi.geekxd</groupId>
        <version>3.9.0-G</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-message</artifactId>

    <description>
        message消息模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>

         <!-- 系统框架 -->
        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-auth-starter</artifactId>
        </dependency>

    </dependencies>

</project>