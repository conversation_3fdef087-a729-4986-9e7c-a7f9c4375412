<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.flowable.mapper.SysDeployFormMapper">

    <resultMap type="SysDeployForm" id="SysDeployFormResult">
        <result property="id" column="id" />
        <result property="formId" column="form_id" />
        <result property="deployId" column="deploy_id" />
    </resultMap>

    <sql id="selectSysDeployFormVo">
        select id, form_id, deploy_id from sys_deploy_form
    </sql>

    <select id="selectSysDeployFormList" parameterType="SysDeployForm" resultMap="SysDeployFormResult">
        <include refid="selectSysDeployFormVo"/>
        <where>
            <if test="formId != null "> and form_id = #{formId}</if>
            <if test="deployId != null  and deployId != ''"> and deploy_id = #{deployId}</if>
        </where>
    </select>

    <select id="selectSysDeployFormById" parameterType="Long" resultMap="SysDeployFormResult">
        <include refid="selectSysDeployFormVo"/>
        where id = #{id}
    </select>

    <select id="selectSysDeployFormByDeployId" resultType="FormTemplate">
        select t1.form_schema as formSchema,t1.form_name as formName,t1.form_id as formId from form_template t1 
        left join sys_deploy_form t2 on t1.form_id = t2.form_id
        where t2.deploy_id = #{deployId} limit 1
    </select>

    <insert id="insertSysDeployForm" parameterType="FormTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into sys_deploy_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formId != null">form_id,</if>
            <if test="deployId != null">deploy_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formId != null">#{formId},</if>
            <if test="deployId != null">#{deployId},</if>
        </trim>
    </insert>

    <update id="updateSysDeployForm" parameterType="SysDeployForm">
        update sys_deploy_form
        <if test="id != null">
            <trim prefix="SET" suffixOverrides=",">
                <if test="formId != null">form_id = #{formId},</if>
                <if test="deployId != null">deploy_id = #{deployId},</if>
            </trim>
            where id = #{id}
        </if>
        <if test="id == null and deployId != null">
            <trim prefix="SET" suffixOverrides=",">
                <if test="formId != null">form_id = #{formId},</if>
            </trim>
            where deploy_id = #{deployId}
        </if>
    </update>

    <delete id="deleteSysDeployFormById" parameterType="Long">
        delete from sys_deploy_form where id = #{id}
    </delete>

    <delete id="deleteSysDeployFormByIds" parameterType="String">
        delete from sys_deploy_form where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>