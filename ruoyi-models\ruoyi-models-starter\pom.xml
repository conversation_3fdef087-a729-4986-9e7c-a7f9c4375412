<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi-models</artifactId>
        <groupId>com.ruoyi.geekxd</groupId>
        <version>3.9.0-G</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-models-starter</artifactId>

    <description>
        中间件
    </description>

    <dependencies>

        <!-- 定时任务模块 -->
        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-quartz</artifactId>
        </dependency>

        <!-- 代码生成模块 -->
        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-generator</artifactId>
        </dependency>

        <!-- 在线开发模块 -->
        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-online</artifactId>
        </dependency>

        <!-- 消息模块 -->
        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-message</artifactId>
        </dependency>

        <!-- 消息模块-->
        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-form</artifactId>
        </dependency>

        <!-- flowable模块-->
        <dependency>
            <groupId>com.ruoyi.geekxd</groupId>
            <artifactId>ruoyi-flowable</artifactId>
        </dependency>

    </dependencies>

</project>
