-- ----------------------------
-- 订单表
-- ----------------------------
DROP TABLE IF EXISTS pay_order;
CREATE TABLE pay_order (
  order_id         bigint          NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  order_number     varchar(255)    NULL DEFAULT NULL,
  third_number     varchar(255)    NULL DEFAULT NULL,
  order_status     varchar(255)    NULL DEFAULT NULL,
  total_amount     varchar(255)    NULL DEFAULT NULL,
  actual_amount    varchar(255)    NULL DEFAULT NULL,
  order_content    varchar(255)    NULL DEFAULT NULL,
  order_message    varchar(255)    NULL DEFAULT NULL,
  create_by        varchar(64)     default '',
  create_time      timestamp       default CURRENT_TIMESTAMP,
  update_by        varchar(64)     default '',
  update_time      timestamp       default CURRENT_TIMESTAMP,
  remark           varchar(500)    default null,
  pay_type         varchar(255)    NULL DEFAULT NULL, -- 支付方式
  pay_time         timestamp       default NULL,      -- 支付时间
  pay_by           varchar(64)     default ''         -- 支付人
);

COMMENT ON COLUMN pay_order.order_id IS '订单id';
COMMENT ON COLUMN pay_order.order_number IS '订单号';
COMMENT ON COLUMN pay_order.third_number IS '第三方订单号';
COMMENT ON COLUMN pay_order.order_status IS '订单状态';
COMMENT ON COLUMN pay_order.total_amount IS '订单总金额';
COMMENT ON COLUMN pay_order.actual_amount IS '实际支付金额';
COMMENT ON COLUMN pay_order.order_content IS '订单内容';
COMMENT ON COLUMN pay_order.order_message IS '负载信息';
COMMENT ON COLUMN pay_order.create_by IS '创建者';
COMMENT ON COLUMN pay_order.create_time IS '创建时间';
COMMENT ON COLUMN pay_order.update_by IS '更新者';
COMMENT ON COLUMN pay_order.update_time IS '更新时间';
COMMENT ON COLUMN pay_order.remark IS '备注';
COMMENT ON COLUMN pay_order.pay_type IS '支付方式';
COMMENT ON COLUMN pay_order.pay_time IS '支付时间';
COMMENT ON COLUMN pay_order.pay_by IS '支付人';

-- ----------------------------
-- 发票表
-- ----------------------------
DROP TABLE IF EXISTS pay_invoice;
CREATE TABLE pay_invoice (
  invoice_id       bigint          NOT NULL GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  order_number     varchar(255)    NULL DEFAULT NULL,
  invoice_type     varchar(255)    NULL DEFAULT NULL,
  invoice_header   varchar(255)    NULL DEFAULT NULL,
  invoice_number   varchar(255)    NULL DEFAULT NULL,
  invoice_phone    varchar(255)    NULL DEFAULT NULL,
  invoice_email    varchar(255)    NULL DEFAULT NULL,
  invoice_remark   varchar(255)    NULL DEFAULT NULL,
  create_by        varchar(64)     default '',
  create_time      timestamp       default CURRENT_TIMESTAMP,
  update_by        varchar(64)     default '',
  update_time      timestamp       default CURRENT_TIMESTAMP,
  remark           varchar(500)    default null
);

COMMENT ON COLUMN pay_invoice.invoice_id IS '发票id';
COMMENT ON COLUMN pay_invoice.order_number IS '订单号';
COMMENT ON COLUMN pay_invoice.invoice_type IS '发票类型';
COMMENT ON COLUMN pay_invoice.invoice_header IS '发票抬头';
COMMENT ON COLUMN pay_invoice.invoice_number IS '纳税人识别号';
COMMENT ON COLUMN pay_invoice.invoice_phone IS '收票人手机号';
COMMENT ON COLUMN pay_invoice.invoice_email IS '收票人邮箱';
COMMENT ON COLUMN pay_invoice.invoice_remark IS '发票备注';
COMMENT ON COLUMN pay_invoice.create_by IS '创建者';
COMMENT ON COLUMN pay_invoice.create_time IS '创建时间';
COMMENT ON COLUMN pay_invoice.update_by IS '更新者';
COMMENT ON COLUMN pay_invoice.update_time IS '更新时间';
COMMENT ON COLUMN pay_invoice.remark IS '备注';

SELECT setval('sys_menu_menu_id_seq', max(menu_id)) FROM sys_menu WHERE menu_id < 100;
-- 插入支付管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('支付管理', 0, 4, 'pay', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'money', 'admin', CURRENT_TIMESTAMP, '', NULL, '');
DO $$
DECLARE
    parentId INTEGER;
    payParentId INTEGER;
BEGIN
    SELECT LASTVAL() INTO parentId;
    SELECT LASTVAL() INTO payParentId;
    
    -- 插入订单菜单
    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('订单', payParentId, '1', 'order', 'pay/order/index', '', 1, 0, 'C', '0', '0', 'pay:order:list', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '订单菜单')
    RETURNING menu_id INTO parentId;

    -- 插入订单按钮
    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('订单查询', parentId, '1', '#', '', '', 1, 0, 'F', '0', '0', 'pay:order:query', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('订单新增', parentId, '2', '#', '', '', 1, 0, 'F', '0', '0', 'pay:order:add', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('订单修改', parentId, '3', '#', '', '', 1, 0, 'F', '0', '0', 'pay:order:edit', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('订单删除', parentId, '4', '#', '', '', 1, 0, 'F', '0', '0', 'pay:order:remove', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('订单导出', parentId, '5', '#', '', '', 1, 0, 'F', '0', '0', 'pay:order:export', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    -- 插入发票菜单
    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('发票', parentId, '1', 'invoice', 'pay/invoice/index', '', 1, 0, 'C', '0', '0', 'pay:invoice:list', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '发票菜单')
    RETURNING menu_id INTO parentId;

    -- 插入发票按钮
    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('发票查询', payParentId, '1', '#', '', '', 1, 0, 'F', '0', '0', 'pay:invoice:query', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('发票新增', parentId, '2', '#', '', '', 1, 0, 'F', '0', '0', 'pay:invoice:add', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('发票修改', parentId, '3', '#', '', '', 1, 0, 'F', '0', '0', 'pay:invoice:edit', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('发票删除', parentId, '4', '#', '', '', 1, 0, 'F', '0', '0', 'pay:invoice:remove', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');

    INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
    VALUES ('发票导出', parentId, '5', '#', '', '', 1, 0, 'F', '0', '0', 'pay:invoice:export', '#', 'admin', CURRENT_TIMESTAMP, '', NULL, '');
END $$;