<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.online.mapper.OnlineMbMapper">
    
    <resultMap type="OnlineMb" id="OnlineMbResult">
        <result property="mbId"    column="mb_id"    />
        <result property="tag"    column="tag"    />
        <result property="tagId"    column="tag_id"    />
        <result property="parameterType"    column="parameter_type"    />
        <result property="resultMap"    column="result_map"    />
        <result property="sqlText"    column="sql_text"    />
        <result property="path"    column="path"    />
        <result property="method"    column="method"    />
        <result property="resultType"    column="result_type"    />
        <result property="actuator"    column="actuator"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="permissionValue"    column="permission_value"    />
    </resultMap>

    <sql id="selectOnlineMbVo">
        select mb_id, tag, tag_id, parameter_type, result_map, sql_text, online_mb.path, method, result_type, actuator,user_id,dept_id,permission_type,permission_value from online_mb
    </sql>

    <select id="selectOnlineMbList" parameterType="OnlineMb" resultMap="OnlineMbResult">
        <include refid="selectOnlineMbVo"/>
        <where>  
            online_mb.del_flag != '1'
            <if test="tag != null  and tag != ''"> and tag = #{tag}</if>
            <if test="tagId != null  and tagId != ''"> and tag_id = #{tagId}</if>
            <if test="parameterType != null  and parameterType != ''"> and parameter_type = #{parameterType}</if>
            <if test="resultMap != null  and resultMap != ''"> and result_map = #{resultMap}</if>
            <if test="sqlText != null  and sqlText != ''"> and sql_text = #{sqlText}</if>
            <if test="path != null  and path != ''"> and path = #{path}</if>
            <if test="method != null  and method != ''"> and method = #{method}</if>
            <if test="resultType != null  and resultType != ''"> and result_type = #{resultType}</if>
            <if test="actuator != null  and actuator != ''"> and actuator = #{actuator}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="deptId != null  and deptId != ''"> and dept_id = #{deptId}</if>
            <if test="permissionType != null  and permissionType != ''"> and permission_type = #{permissionType}</if>
            <if test="permissionValue != null  and permissionValue != ''"> and permission_value = #{permissionValue}</if>
        </where>
    </select>
    
    <select id="selectOnlineMbByMbId" parameterType="Long" resultMap="OnlineMbResult">
        <include refid="selectOnlineMbVo"/>
        where online_mb.mb_id = #{mbId}  and online_mb.del_flag != '1'
    </select>
        
    <insert id="insertOnlineMb" parameterType="OnlineMb">
        insert into online_mb
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mbId != null">mb_id,</if>
            <if test="tag != null">tag,</if>
            <if test="tagId != null">tag_id,</if>
            <if test="parameterType != null">parameter_type,</if>
            <if test="resultMap != null">result_map,</if>
            <if test="sqlText != null">sql_text,</if>
            <if test="path != null and path != ''">path,</if>
            <if test="method != null and method != ''">method,</if>
            <if test="resultType != null">result_type,</if>
            <if test="actuator != null and actuator != ''">actuator,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="deptId != null and deptId != ''">dept_id,</if>
            <if test="permissionType != null and permissionType != ''">permission_type,</if>
            <if test="permissionValue != null and permissionValue != ''">permission_value,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mbId != null">#{mbId},</if>
            <if test="tag != null">#{tag},</if>
            <if test="tagId != null">#{tagId},</if>
            <if test="parameterType != null">#{parameterType},</if>
            <if test="resultMap != null">#{resultMap},</if>
            <if test="sqlText != null">#{sqlText},</if>
            <if test="path != null and path != ''">#{path},</if>
            <if test="method != null and method != ''">#{method},</if>
            <if test="resultType != null">#{resultType},</if>
            <if test="actuator != null and actuator != ''">#{actuator},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="deptId != null and deptId != ''">#{deptId},</if>
            <if test="permissionType != null and permissionType != ''">#{permissionType},</if>
            <if test="permissionValue != null and permissionValue != ''">#{permissionValue},</if>
         </trim>
    </insert>

    <update id="updateOnlineMb" parameterType="OnlineMb">
        update online_mb
        <trim prefix="SET" suffixOverrides=",">
            <if test="tag != null">tag = #{tag},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="parameterType != null">parameter_type = #{parameterType},</if>
            <if test="resultMap != null">result_map = #{result_map},</if>
            <if test="sqlText != null">sql_text = #{sqlText},</if>
            <if test="path != null and path != ''">path = #{path},</if>
            <if test="method != null and method != ''">method = #{method},</if>
            <if test="resultType != null">result_type = #{resultType},</if>
            <if test="actuator != null and actuator != ''">actuator = #{actuator},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="permissionType != null and permissionType != ''">permission_type = #{permissionType},</if>
            <if test="permissionValue != null and permissionValue != ''">permission_value = #{permissionValue},</if>
        </trim>
        where online_mb.mb_id = #{mbId} and online_mb.del_flag != '1'
    </update>

    <delete id="deleteOnlineMbByMbId" parameterType="Long">
        update online_mb set del_flag = '1' where mb_id = #{mbId}
    </delete>

    <delete id="deleteOnlineMbByMbIds" parameterType="String">
        update online_mb set del_flag = '1' where mb_id in 
        <foreach item="mbId" collection="array" open="(" separator="," close=")">
            #{mbId}
        </foreach>
    </delete>
</mapper>